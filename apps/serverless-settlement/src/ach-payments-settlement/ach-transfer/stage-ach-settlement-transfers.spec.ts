// Create a date that is 3 days earlier than the current date
const threeDaysAgo = new Date();
threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
import { HANDLER_CONTEXT_MOCK_DATA } from '../../test/test-helpers';
import { SettlementError } from '../../types';
import { getAchPaymentReleasedDate, handler } from './stage-ach-settlement-transfers';

jest.mock('@dbd/analytics-common', () => {
  const getReceivedBankRefundsMock = jest.fn().mockImplementation(() => ({
    success: true,
    value: [
      {
        id: 'refund-id',
        account_id: 'account-id',
      },
    ],
  }));

  const getReceivedReversalsMock = jest.fn().mockImplementation(() => ({
    success: true,
    value: [
      {
        payment_id: 'payment-id',
        account_id: 'account-id',
      },
      {
        payment_id: 'payment-id-2',
        account_id: 'account-id-2',
        skip_funds_transfer: true,
      },
    ],
  }));

  const getReceivedBankPaymentIdsMock = jest.fn().mockImplementation(() => ({
    success: true,
    value: [
      {
        id: 'payment-id',
      },
      {
        id: 'payment-id-2',
      },
    ],
  }));

  const getBankPaymentsWithBankGatewaySettingsMock = jest.fn().mockImplementation(() => {
    return {
      success: true,
      value: [
        {
          id: 'payment-id',
          payment_intent_id: 'payment-intent-id',
          entity: 'payment',
          received_at: '2024-02-01T00:00:00.000Z',
          bank_gateway_settings: {
            gateways: [
              {
                id: 'gateway-id',
                payout_delay: 1,
              },
            ],
          },
          gateway_id: 'gateway-id',
        },
      ],
    };
  });

  const analyticsClientMock = {
    getReceivedBankRefunds: getReceivedBankRefundsMock,
    getReceivedReversals: getReceivedReversalsMock,
    getBankPaymentsWithBankGatewaySettings: getBankPaymentsWithBankGatewaySettingsMock,
    getReceivedBankPaymentIds: getReceivedBankPaymentIdsMock,
  };

  return {
    defaultDynamoAnalyticsClient: jest.fn().mockImplementation(() => analyticsClientMock),
    __mocks: {
      getReceivedBankRefundsMock,
      getReceivedReversalsMock,
      getBankPaymentsWithBankGatewaySettingsMock,
      getReceivedBankPaymentIdsMock,
    },
  };
});

const { __mocks: analyticsMocks } = jest.requireMock('@dbd/analytics-common');
jest.mock('@dbd/core-db', () => {
  const achSettlementTransferCreateMock = jest.fn().mockResolvedValue({
    id: 'batch-id',
  });
  const bankFundingBatchGetMock = jest.fn().mockResolvedValue({
    data: [
      {
        id: 'batch-id',
        status: 'created',
        fundingData: {
          fundingId: 'funding-id',
        },
      },
    ],
  });
  const paymentsGetOneByIdMock = jest.fn().mockResolvedValue({
    id: 'payment-id',
    paymentIntentId: 'payment-intent-id',
    entity: 'payment',
    amount: 100,
    accountId: 'account-id',
    bankGatewayAuthResponse: { foo: 'bar' },
    status: 'captured',
    receivedAt: '2024-02-01T00:00:00.000Z',
    gatewayId: 'gateway-id',
  });
  const paymentSettingsGetOneMock = jest.fn().mockResolvedValue({
    accountId: 'account-id',
    bankGatewaySettings: {
      gateways: [
        {
          id: 'gateway-id',
          gatewayId: 'gateway-id',
          payoutDelay: 1,
        },
      ],
    },
  });
  const getReversalsMockData = jest.fn().mockResolvedValue({
    settlementStatus: 'settled',
  });

  return {
    PaymentRepo: jest.fn().mockImplementation(() => ({
      achSettlementTransfer: {
        create: achSettlementTransferCreateMock,
      },
      bankFundingBatch: {
        getByStatus: bankFundingBatchGetMock,
      },
      payments: {
        getOneById: paymentsGetOneByIdMock,
      },
      paymentSettings: {
        getOne: paymentSettingsGetOneMock,
      },
      reversals: {
        getOneByPaymentId: getReversalsMockData,
      },
    })),
    __mocks: {
      achSettlementTransferCreateMock,
      paymentsGetOneByIdMock,
      getReversalsMockData,
    },
  };
});

const { __mocks: coreDbMocks } = jest.requireMock('@dbd/core-db');

jest.mock('@aws-sdk/client-lambda', () => {
  const lambdaClientSendMock = jest.fn().mockResolvedValue({});
  return {
    ...jest.requireActual('@aws-sdk/client-lambda'),
    LambdaClient: jest.fn().mockImplementation(() => {
      return {
        send: lambdaClientSendMock,
      };
    }),
    __mocks: {
      lambdaClientSendMock,
    },
  };
});

const { __mocks: lambdaMocks } = jest.requireMock('@aws-sdk/client-lambda');

jest.mock('@dbd/grailpay-sdk', () => {
  const grailpayGetPayoutMock = jest.fn().mockResolvedValue({
    payeeId: [
      {
        transactions: [
          {
            clientReferenceId: 'pmt_1',
          },
        ],
      },
    ],
  });

  return {
    GrailpayClient: jest.fn().mockImplementation(() => ({
      getBatchPayout: grailpayGetPayoutMock,
    })),
    __mocks: {
      grailpayGetPayoutMock,
    },
  };
});

describe('stage-ach-settlement-transfers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('Should return err with no owner_id in event', async () => {
    const res = await handler({ owner_id: undefined as unknown as string }, HANDLER_CONTEXT_MOCK_DATA);
    expect(res).toBeInstanceOf(SettlementError);
    expect(res?.name).toBe('INVALID_EVENT');
    expect(res?.message).toBe('Owner Id missing from event');
  });

  it('Should stage ach settlement transfers for mix of payments and refunds', async () => {
    analyticsMocks.getReceivedReversalsMock.mockResolvedValueOnce({ success: true, value: [] });
    const event = {
      owner_id: 'owner-id',
    };
    const res = await handler(event, HANDLER_CONTEXT_MOCK_DATA);
    const sendCommandArgs = lambdaMocks.lambdaClientSendMock.mock.calls[0][0].input;
    const commandPayload = JSON.parse(sendCommandArgs.Payload.toString());

    expect(analyticsMocks.getReceivedBankRefundsMock.mock.calls[0][0]).toStrictEqual({ tenantId: 'owner-id' });
    expect(coreDbMocks.achSettlementTransferCreateMock.mock.calls[0][0]).toStrictEqual({
      records: [
        {
          id: 'payment-id',
          paymentIntentId: 'payment-intent-id',
          entity: 'payment',
        },
        {
          id: 'refund-id',
          accountId: 'account-id',
          entity: 'refund',
        },
      ],
      tenantId: 'owner-id',
    });
    expect(sendCommandArgs.FunctionName).toBe('processAchSettleTransfers');
    expect(commandPayload).toStrictEqual({
      owner_id: 'owner-id',
    });
    expect(res).toBe(undefined);
  });

  it('Should stage ach settlement transfers for only payments', async () => {
    analyticsMocks.getReceivedBankRefundsMock.mockResolvedValueOnce({ success: true, value: [] });
    analyticsMocks.getReceivedReversalsMock.mockResolvedValueOnce({ success: true, value: [] });
    analyticsMocks.getReceivedBankPaymentIdsMock.mockResolvedValueOnce({
      success: true,
      value: [{ id: 'payment-id' }, { id: 'payment-id-2' }],
    });
    const event = {
      owner_id: 'owner-id',
    };
    const res = await handler(event, HANDLER_CONTEXT_MOCK_DATA);
    const sendCommandArgs = lambdaMocks.lambdaClientSendMock.mock.calls[0][0].input;
    const commandPayload = JSON.parse(sendCommandArgs.Payload.toString());

    expect(analyticsMocks.getReceivedBankRefundsMock.mock.calls[0][0]).toStrictEqual({ tenantId: 'owner-id' });
    expect(analyticsMocks.getReceivedBankPaymentIdsMock.mock.calls[0][0]).toStrictEqual({ tenantId: 'owner-id' });

    expect(coreDbMocks.achSettlementTransferCreateMock.mock.calls[0][0]).toStrictEqual({
      records: [
        {
          id: 'payment-id',
          paymentIntentId: 'payment-intent-id',
          entity: 'payment',
        },
      ],
      tenantId: 'owner-id',
    });
    expect(sendCommandArgs.FunctionName).toBe('processAchSettleTransfers');
    expect(commandPayload).toStrictEqual({
      owner_id: 'owner-id',
    });
    expect(res).toBe(undefined);
  });

  it('Should stage ach settlement transfers for only refunds', async () => {
    analyticsMocks.getBankPaymentsWithBankGatewaySettingsMock.mockResolvedValueOnce({ success: true, value: [] });
    analyticsMocks.getReceivedReversalsMock.mockResolvedValueOnce({ success: true, value: [] });
    coreDbMocks.paymentsGetOneByIdMock.mockResolvedValueOnce(undefined);
    const event = {
      owner_id: 'owner-id',
    };
    const res = await handler(event, HANDLER_CONTEXT_MOCK_DATA);
    const sendCommandArgs = lambdaMocks.lambdaClientSendMock.mock.calls[0][0].input;
    const commandPayload = JSON.parse(sendCommandArgs.Payload.toString());
    expect(analyticsMocks.getReceivedBankRefundsMock.mock.calls[0][0]).toStrictEqual({ tenantId: 'owner-id' });
    expect(coreDbMocks.achSettlementTransferCreateMock.mock.calls[0][0]).toStrictEqual({
      records: [
        {
          id: 'refund-id',
          accountId: 'account-id',
          entity: 'refund',
        },
      ],
      tenantId: 'owner-id',
    });
    expect(sendCommandArgs.FunctionName).toBe('processAchSettleTransfers');
    expect(commandPayload).toStrictEqual({
      owner_id: 'owner-id',
    });
    expect(res).toBe(undefined);
  });

  it('Should stage ach settlement transfers for only reversals', async () => {
    analyticsMocks.getBankPaymentsWithBankGatewaySettingsMock.mockResolvedValueOnce({ success: true, value: [] });
    analyticsMocks.getReceivedBankRefundsMock.mockResolvedValueOnce({ success: true, value: [] });
    coreDbMocks.paymentsGetOneByIdMock.mockResolvedValueOnce(undefined);
    const event = {
      owner_id: 'owner-id',
    };
    const res = await handler(event, HANDLER_CONTEXT_MOCK_DATA);
    const sendCommandArgs = lambdaMocks.lambdaClientSendMock.mock.calls[0][0].input;
    const commandPayload = JSON.parse(sendCommandArgs.Payload.toString());
    expect(analyticsMocks.getReceivedReversalsMock.mock.calls[0][0]).toStrictEqual({ tenantId: 'owner-id' });
    expect(coreDbMocks.achSettlementTransferCreateMock.mock.calls[0][0]).toStrictEqual({
      records: [
        {
          accountId: 'account-id',
          paymentId: 'payment-id',
          entity: 'reversal',
        },
      ],
      tenantId: 'owner-id',
    });
    expect(coreDbMocks.achSettlementTransferCreateMock.mock.calls[1][0]).toStrictEqual({
      records: [
        {
          accountId: 'account-id-2',
          paymentId: 'payment-id-2',
          entity: 'reversal',
        },
      ],
      tenantId: 'owner-id',
      skipFundsTransfer: true,
      skipFundsTransferReason: 'reversal_funded_by_payment',
      holdTxsAtSettlement: false,
    });
    expect(sendCommandArgs.FunctionName).toBe('processAchSettleTransfers');
    expect(commandPayload).toStrictEqual({
      owner_id: 'owner-id',
    });
    expect(res).toBe(undefined);
  });
});

describe('getAchPaymentReleasedDate', () => {
  it('Should return the release date', () => {
    const releaseDate = getAchPaymentReleasedDate('2025-06-11T12:00:00.000Z', 1);
    expect(releaseDate.toISOString()).toBe('2025-06-12T04:00:00.000Z');
  });

  it('Should calculate exact release dates', () => {
    // 4pm EST June 11th 2025 (Daylight Savings Time)
    expect(getAchPaymentReleasedDate('2025-06-11T16:00:00.000Z', 0).toISOString()).toBe('2025-06-11T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T16:00:00.000Z', 1).toISOString()).toBe('2025-06-12T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T16:00:00.000Z', 2).toISOString()).toBe('2025-06-13T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T16:00:00.000Z', 3).toISOString()).toBe('2025-06-14T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T16:00:00.000Z', 4).toISOString()).toBe('2025-06-15T04:00:00.000Z');

    // 9am EST June 11th 2025 (Daylight Savings Time)
    expect(getAchPaymentReleasedDate('2025-06-11T13:00:00.000Z', 0).toISOString()).toBe('2025-06-11T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T13:00:00.000Z', 1).toISOString()).toBe('2025-06-12T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T13:00:00.000Z', 2).toISOString()).toBe('2025-06-13T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T13:00:00.000Z', 3).toISOString()).toBe('2025-06-14T04:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-06-11T13:00:00.000Z', 4).toISOString()).toBe('2025-06-15T04:00:00.000Z');

    // 9am EST Jan 1st 2025 (Standard Time)
    expect(getAchPaymentReleasedDate('2025-01-01T13:00:00.000Z', 0).toISOString()).toBe('2025-01-01T05:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-01-01T13:00:00.000Z', 1).toISOString()).toBe('2025-01-02T05:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-01-01T13:00:00.000Z', 2).toISOString()).toBe('2025-01-03T05:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-01-01T13:00:00.000Z', 3).toISOString()).toBe('2025-01-04T05:00:00.000Z');
    expect(getAchPaymentReleasedDate('2025-01-01T13:00:00.000Z', 4).toISOString()).toBe('2025-01-05T05:00:00.000Z');
  });
});
