import { InvokeCommand } from '@aws-sdk/client-lambda';
import { AchSettlementTransferSkipFundsTransferReason } from '@dbd/core-types/constants/ach-settlement-transfer.constants';
import { AchSettlementTransfer } from '@dbd/core-types/domain/ach-settlement-transfer.domain';
import { Logger, LogWithRequestTracing } from '@dbd/serverless-logger';
import { batchArray } from '@dbd/utils';
import { EntityType } from '@dbd/zod-types-common';
import type { Context } from 'aws-lambda';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { analyticsClient } from '../../lib/analytics-client';
import { SettlementError } from '../../types';
import { lambdaClient } from '../lib/lambda-client';
import { paymentsRepoClient as paymentsRepo } from '../lib/payment-repo-client';

dayjs.extend(utc);
dayjs.extend(timezone);

const DYNAMO_BATCH_SIZE = 5000;

/**
 * getAchPaymentReleasedDate()
 *
 * @description
 * Calculate the release date for an ach payment.
 * The release date should be flattened to the start of the day in eastern time
 * EX. received_at: 2025-06-11T12:00:00.000Z
 * release_date: 2025-06-12T05:00:00.000Z
 *
 * @param receivedAt - The ISO 8601 date/time the payment funding was received
 * @param holdDays - The number of settlement days the funding should be held
 * @returns The release date
 */
export const getAchPaymentReleasedDate = (receivedAt: string, holdDays: number) => {
  return dayjs(receivedAt).tz('America/New_York').add(holdDays, 'day').startOf('day').toDate();
};

/**
 * @remarks
 * Fetch payments that are ready to stage for ach settlement transfers.
 *
 * @param ownerId - The tenant ID.
 */

export const getPaymentsToStage = async (ownerId: string) => {
  const client = await analyticsClient;

  // TODO: Add index to a payment entity and query them directly
  const paymentIdsResult = await client.getReceivedBankPaymentIds({ tenantId: ownerId });

  if (!paymentIdsResult.success) {
    Logger.error(paymentIdsResult.error, 'Error getting received bank payment ids');
    throw paymentIdsResult.error;
  }
  const paymentIds = paymentIdsResult.value.map(({ id }) => id);

  // Get all received bank payments with bank gateway settings from warehouse that have not been settled.
  const result = await client.getBankPaymentsWithBankGatewaySettings({ tenantId: ownerId, paymentIds });
  if (!result.success) {
    Logger.error(result.error, 'Error getting bank payments with bank gateway settings');
    throw result.error;
  }
  const paymentsData = result.value;
  const paymentsToRelease: { id: string; paymentIntentId: string; entity: typeof EntityType.Payment }[] = [];
  const paymentsToHold: { id: string; paymentIntentId: string; entity: typeof EntityType.Payment }[] = [];

  Logger.info(
    {
      data: {
        payments: paymentsData,
      },
    },
    `Processing ${paymentsData.length} payments for ach settlement transfer staging`,
  );
  // identify which payments are eligible for release
  paymentsData.forEach((payment) => {
    const usedGateway = payment.bank_gateway_settings.gateways.find((gw) => gw.id === payment.gateway_id);
    // if the payment has been received and has a gateway, then we can calculate the release date
    if (usedGateway && payment.received_at) {
      const releaseDate = getAchPaymentReleasedDate(payment.received_at, usedGateway.payout_delay);
      const currentDate = new Date();
      // if the current date is greater than or equal to the release date, or the release in days is 0, then the payment is eligible for release
      if (currentDate >= releaseDate) {
        const paymentData = {
          id: payment.id,
          paymentIntentId: payment.payment_intent_id,
          entity: EntityType.Payment,
        };
        if (payment.reversal_pending) {
          paymentsToHold.push(paymentData);
        } else {
          paymentsToRelease.push(paymentData);
        }
      }
    }
  });

  Logger.info(
    { data: paymentsToRelease },
    `Found ${paymentsToRelease.length} payments to stage for ach settlement transfer`,
  );
  Logger.info({ data: paymentsToHold }, `Found ${paymentsToHold.length} payments to hold for ach settlement transfer`);
  return { stage: paymentsToRelease, hold: paymentsToHold };
};

/**
 * @remarks
 * Fetch refunds that are ready to stage for ach settlement transfers.
 *
 * @param ownerId - The tenant ID.
 */
export const getRefundsToStage = async (ownerId: string) => {
  const client = await analyticsClient;
  // Get all received bank payments with bank gateway settings from warehouse that have not been settled.
  const result = await client.getReceivedBankRefunds({ tenantId: ownerId });
  if (!result.success) {
    Logger.error(result.error, 'Error getting received bank refunds');
    throw result.error;
  }
  // map the refunds to the required format
  const refundsData = result.value.map(({ id, account_id: accountId }) => ({
    id,
    accountId,
    entity: EntityType.Refund,
  }));

  Logger.info({ data: result }, `Found ${result.value.length} refunds to stage for ach settlement transfer`);
  return { stage: refundsData };
};

export const getReversalsToStage = async (ownerId: string) => {
  const client = await analyticsClient;
  const result = await client.getReceivedReversals({ tenantId: ownerId });
  if (!result.success) {
    Logger.error(result.error, 'Error getting received reversals');
    throw result.error;
  }
  const { value: reversals } = result;
  const reversalsToRelease: { paymentId: string; accountId: string; entity: typeof EntityType.Reversal }[] = [];
  const reversalsToHold: { paymentId: string; accountId: string; entity: typeof EntityType.Reversal }[] = [];
  // iterate over the reversals and identify which ones are eligible for release
  for (const { payment_id: paymentId, account_id: accountId, skip_funds_transfer: skipFundsTransfer } of reversals) {
    const reversalData = {
      paymentId,
      accountId,
      entity: EntityType.Reversal,
    };
    if (skipFundsTransfer) {
      reversalsToHold.push(reversalData);
    } else {
      reversalsToRelease.push(reversalData);
    }
  }
  Logger.info(
    {
      data: {
        reversalsToRelease,
        reversalsToHold,
      },
    },
    `Found ${reversalsToRelease.length + reversalsToHold.length} reversals to stage for ach settlement transfer`,
  );

  if (reversalsToHold.length > 0) {
    Logger.info({ data: reversalsToHold }, `Skipping funds transfer for ${reversalsToHold.length} reversals`);
  }

  return { stage: reversalsToRelease, hold: reversalsToHold };
};

/**
 * @remarks
 * This function stages ach settlement transfers for payments, refunds and reversals. It retrieves records from warehouse that have been received and are ready to be processed.
 * The function then creates a batch record using `paymentsRepo.achSettlementTransfer.create` and updates the records with the `achSettlementTransferId`.
 *
 * @param event - The event object.
 */
export const handler = async (event: { owner_id: string }, context: Context) => {
  LogWithRequestTracing(event, context);
  Logger.info({ data: event }, `Stage ACH Settlement Transfers`);
  try {
    const ownerId = event.owner_id;

    if (!ownerId) {
      Logger.error('Owner Id missing from event');
      throw new SettlementError({ name: 'INVALID_EVENT', message: 'Owner Id missing from event' });
    }

    /*
    Since dynamoDb has limit of 10k, we use DYNAMO_BATCH_SIZE to split records into batches
    and create a batchPush for each batch
    */
    const { stage: paymentsToStage, hold: paymentsToHold } = await getPaymentsToStage(ownerId);
    const { stage: refundsToStage } = await getRefundsToStage(ownerId);
    const { stage: reversalsToStage, hold: reversalsToHold } = await getReversalsToStage(ownerId);
    const allToStage = [...paymentsToStage, ...refundsToStage, ...reversalsToStage];
    const results: AchSettlementTransfer[] = [];
    // split the records into batches
    const batchesToStage = batchArray(allToStage, DYNAMO_BATCH_SIZE);
    // Stage ACH Settlement Transfer
    for (const batch of batchesToStage) {
      const payload = {
        records: batch,
        tenantId: ownerId,
      };
      // Create transfer record, status = staged
      const achSettlementTransfer = await paymentsRepo.achSettlementTransfer.create(payload);
      if (achSettlementTransfer) {
        results.push(achSettlementTransfer);
      }
    }
    // Create ACH Settlement Transfer for payments that have a pending reversal.
    if (paymentsToHold.length > 0) {
      const heldPaymentsAchSettlementTransfer = await paymentsRepo.achSettlementTransfer.create({
        records: paymentsToHold,
        tenantId: ownerId,
        skipFundsTransfer: true,
        skipFundsTransferReason: AchSettlementTransferSkipFundsTransferReason.FUNDING_IS_PENDING_REVERSAL,
        holdTxsAtSettlement: true,
      });
      if (heldPaymentsAchSettlementTransfer) {
        results.push(heldPaymentsAchSettlementTransfer);
      }
    }
    // Create ACH Settlement Transfer for reversals that are funded by a payment whos transactions are held.
    if (reversalsToHold.length > 0) {
      const heldReversalsAchSettlementTransfer = await paymentsRepo.achSettlementTransfer.create({
        records: reversalsToHold,
        tenantId: ownerId,
        skipFundsTransfer: true,
        skipFundsTransferReason: AchSettlementTransferSkipFundsTransferReason.REVERSAL_FUNDED_BY_PAYMENT,
        holdTxsAtSettlement: false,
      });
      if (heldReversalsAchSettlementTransfer) {
        results.push(heldReversalsAchSettlementTransfer);
      }
    }
    Logger.info({ data: results }, `Created ${results.length} ACH Settlement Transfers`);

    // Invoke next step to process the bank transfers
    const command = new InvokeCommand({
      FunctionName: process.env.INVOKE_NEXT_LAMBDA || 'processAchSettleTransfers',
      InvocationType: 'Event',
      Payload: Buffer.from(JSON.stringify({ owner_id: ownerId })),
    });
    const result = await lambdaClient.send(command);
    Logger.info({ data: result }, `Invoke Process Payment ACH Settlement Transfers`);

    return;
  } catch (e) {
    Logger.error(e);
    if (e instanceof SettlementError) {
      return e;
    }
    return new SettlementError({ name: 'UNKNOWN_ERROR', message: (e as Error)?.message });
  }
};
