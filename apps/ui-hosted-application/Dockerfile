# Install dependencies only when needed
FROM node:20-slim AS base
RUN apt-get update && \
  apt-get install -y dumb-init openssl && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

WORKDIR /app
ARG BUILD_VERSION=unknown
ENV BUILD_VERSION=$BUILD_VERSION
ENV NEXT_PUBLIC_BUILD_VERSION=$BUILD_VERSION
ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}
ENV NODE_ENV=production
ENV SENTRY_DSN="https://<EMAIL>/4506202708508672"
ENV NEXT_PUBLIC_SENTRY_DSN="https://<EMAIL>/4506202708508672"

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY apps/ui-hosted-application/public ./apps/ui-hosted-application/public
COPY apps/ui-hosted-application/.next/standalone/apps/ui-hosted-application/package.json ./package.json
#RUN yarn global add pnpm@8.11.0 && pnpm i --ignore-scripts
COPY apps/ui-hosted-application/.next/standalone ./
COPY apps/ui-hosted-application/.next/static ./apps/ui-hosted-application/.next/static

# https://github.com/vercel/next.js/discussions/30039
COPY apps/ui-hosted-application/entrypoint.sh /
RUN chmod +x /entrypoint.sh

# https://github.com/vercel/next.js/issues/40735#issuecomment-1314124329
# RUN sed -i 's/server\/next.js/server\/next-server.js/' node_modules/next/package.json

USER nextjs
EXPOSE 3000
ENV PORT 3000
ENV NEXT_TELEMETRY_DISABLED 1

CMD ["dumb-init","node", "--enable-source-maps","apps/ui-hosted-application/server.js"]
