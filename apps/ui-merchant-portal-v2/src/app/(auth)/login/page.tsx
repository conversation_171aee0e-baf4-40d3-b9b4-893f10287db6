import { ErrFailedToSignInWithUsernameAndPassword } from '@dbd/next-sessions/auth/server';
import { Button } from '@dbd/ui/components/button';
import { Card } from '@dbd/ui/components/card';
import { isRedirectNavigationError } from '@dbd/ui/lib/utils';
import { BadRequestResponse, ServerErrorResponse } from '@dbd/zod-types-common';
import Link from 'next/link';

import LoginForm, { LoginFormSchema } from '@/blocks/login-form';
import { merchantAuth } from '@/lib/auth';
import { getPartnerBrandingMetadata } from '@/lib/get-partner-branding';

export default function LoginPage() {
  async function onSubmit(data: LoginFormSchema) {
    'use server';

    try {
      const auth = await merchantAuth();

      const [_, user] = await auth.signInWithUsernameAndPassword({
        username: data.username,
        password: data.password,
      });

      return { success: true as const, data: user };
    } catch (error) {
      if (isRedirectNavigationError(error)) {
        throw error;
      }

      if (error === ErrFailedToSignInWithUsernameAndPassword) {
        return {
          success: false as const,
          error: BadRequestResponse.parse({
            errors: [
              {
                code: '400',
                path: ['root'],
                message: 'Invalid username or password',
              },
            ],
          }),
        };
      }

      return {
        success: false as const,
        error: ServerErrorResponse.parse({
          errors: [
            {
              code: '500',
              path: ['root'],
              message: 'An unknown error occurred',
            },
          ],
        }),
      };
    }
  }

  return (
    <section className="mx-auto flex size-full min-h-screen items-center justify-center bg-background text-foreground">
      <div className="mx-auto h-fit w-full max-w-[480px] bg-background p-4">
        <Card className="p-8">
          <LoginForm onSubmit={onSubmit} />
        </Card>
        <div className="mt-4 flex justify-end text-brand-500">
          <Link href="/auth/login">
            <Button variant="link">Admin login</Button>
          </Link>
        </div>
      </div>
    </section>
  );
}

export async function generateMetadata() {
  return await getPartnerBrandingMetadata({
    type: 'Login',
    canonical: '/auth/login',
  });
}
