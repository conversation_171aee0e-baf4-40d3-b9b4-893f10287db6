'use client';

import { AuthUser } from '@dbd/reporting-auth/auth.types.js';
import { Button } from '@dbd/ui/components/button';
import { Form, FormField } from '@dbd/ui/components/form';
import { useBranding } from '@dbd/ui/providers/branding-provider';
import { TextInput } from '@dbd/ui-forms/components/text-form-input';
import { useHookForm, UseHookFormProps } from '@dbd/ui-forms/hooks/use-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { FieldError } from 'react-hook-form';
import { z } from 'zod';

export const LoginFormSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(8, 'Password must be at least 8 characters long'),
});
export type LoginFormSchema = z.infer<typeof LoginFormSchema>;

export default function LoginForm(props: UseHookFormProps<LoginFormSchema, AuthUser>) {
  const branding = useBranding();

  const { form, handleSubmit, fatalError } = useHookForm<LoginFormSchema, AuthUser>({
    resolver: zodResolver(LoginFormSchema),
    defaultValues: {
      username: '',
      password: '',
    },
    onSuccess: async (data) => {
      await props.onSuccess?.(data);
      window.location.href = '/dashboard';
    },
    ...props,
  });

  const error = (form.formState.errors as Record<string, FieldError>)?.root?.message;
  const errorMessage = error ?? (fatalError ? 'An unknown error occurred' : null);

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="size-full">
        <section className="flex w-full flex-col gap-4">
          {branding && (
            <div className="flex items-center justify-center">
              <div className="grid gap-1 text-left text-sm leading-tight">
                <span className="truncate text-xl font-semibold">{branding.title}</span>
                <span className="truncate text-xs">Merchant Portal</span>
              </div>
            </div>
          )}

          {errorMessage && (
            <div className="flex w-full flex-col gap-4">
              <p className="text-red-500">{errorMessage}</p>
            </div>
          )}

          <FormField
            required
            name="username"
            render={({ field }) => <TextInput className="w-full" field={field} label="Username" />}
          />
          <FormField
            required
            name="password"
            render={({ field }) => <TextInput className="w-full" field={field} type="password" label="Password" />}
          />
          <Link href="/auth/forgot-password">
            <Button variant="link" type="button">
              Forgot password?
            </Button>
          </Link>
          <Button variant="primary" type="submit">
            Login
          </Button>
        </section>
      </form>
    </Form>
  );
}
