# -----------------------------------------------------------------------------
# Step: Copy dist, node_modules and related artifacts
# -----------------------------------------------------------------------------
FROM oven/bun:1 AS base
WORKDIR /app

FROM oven/bun:1 AS artifacts

WORKDIR /app

ENV NODE_ENV=production

ARG SERVICE_NAME=payments-core-service

COPY --chown=node:node apps/payments-core-service/package.json .
COPY --chown=node:node dist/libs/instrumentation/fastify/instrumentation.cjs .

RUN sed -i '/workspace\:/d' ./package.json

COPY --chown=node:node dist/apps/payments-core-service .

RUN bun install --no-lockfile
  # -----------------------------------------------------------------------------
# Step: Create deployed artifact
# -----------------------------------------------------------------------------
FROM 528973710517.dkr.ecr.us-east-1.amazonaws.com/core-apps-base:node-20-slim AS runner
LABEL org.opencontainers.image.source="https://github.com/dbdventures/core-microservices-nx/payments-core-service"
  
# (exclusions in .dockerignore file)
COPY --from=artifacts --chown=node:node  /app /app

ARG BUILD_VERSION=unknown
ENV BUILD_VERSION=$BUILD_VERSION

ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}

ENV NODE_ENV=production

WORKDIR /app

USER node
EXPOSE 3000

ENTRYPOINT [ "tini", "--" ]

CMD ["node", "--enable-source-maps", "--require", "./instrumentation.cjs", "main.cjs"]  