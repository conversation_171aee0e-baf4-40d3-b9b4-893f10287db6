import {
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine1Max,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine1Min,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine2Max,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine2Min,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCityMax,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCityMin,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCountryMax,
  submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCountryMin,
} from '@dbd/account-service-client/merchants-on-boarding-applications/merchants-on-boarding-applications.zod.js';
import { z } from 'zod';

export const ownersSchema = z
  .array(
    z.object({
      id: z.string().optional(),
      birth_date: z.string().optional(),
      email: z.string().optional(),
      first_name: z.string().optional(),
      last_name: z.string().optional(),
      ownership_percent: z.number().nullish(),
      phone_number: z.string().optional(),
      residence_address: z
        .object({
          country: z
            .string()
            .min(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCountryMin)
            .max(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCountryMax),
          state: z.string(),
          city: z
            .string()
            .min(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCityMin)
            .max(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressCityMax),
          zip_code: z.string(),
          address_line1: z
            .string()
            .min(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine1Min)
            .max(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine1Max),
          address_line2: z
            .string()
            .min(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine2Min)
            .max(submitMerchantApplicationDraftBodyOwnersItemResidenceAddressAddressLine2Max)
            .nullable()
            .optional(),
        })
        .optional(),
      signer: z.boolean().optional(),
      ssn: z
        .string()
        .or(z.object({ mask: z.string() }))
        .nullish(),
      ssn_encrypted: z.string().nullish(),
      title: z
        .enum([
          'OWNER',
          'PARTNER',
          'PRESIDENT',
          'VICE_PRESIDENT',
          'SECRETARY',
          'TREASURER',
          'CEO',
          'CFO',
          'COO',
          'MEMBER',
          'NA',
        ])
        .optional(),
    }),
  )
  .min(1)
  .max(10)
  .optional();
