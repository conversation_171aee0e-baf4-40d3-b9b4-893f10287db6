import { z } from 'zod';

import { ownersSchema } from './dtos/ownership.dto.js';

const DBDEncryptedField = z.object({
  mask: z.string(),
});

const LocationReference = z.object({
  country: z.string().max(4),
  state: z.preprocess((value, ctx) => {
    if (!value) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `the value ${value} can not be empty`,
      });
      return z.NEVER;
    }
    return String(value).toUpperCase();
  }, z.string().max(2)),
  city: z.string().max(24),
  zip_code: z.string().max(10),
  address_line1: z.string().max(100),
  address_line2: z.string().max(100).nullish(),
});

const ownershipType = z.enum([
  'GOVERNMENT',
  'SOLO_TRADER',
  'PARTNERSHIP',
  'PUBLIC',
  'PRIVATE',
  'TAX_EXEMPT_ORGANIZATION',
  'LIMITED_LIABILITY_COMPANY',
]);

const applicationType = z.enum(['PARTNER', 'BUSINESS']);

export const PayeeIndividual = z.object({
  address: LocationReference,

  application_id: z.string(),

  bank_identity_manual_account: z
    .object({
      class_type: z.string(),
      id: z.string(),
      mask: z.string(),
      name: z.string(),
      subtype: z.string(),
      type: z.string(),
      verification_status: z.string(),
    })
    .optional(),

  birth_date: z.string().optional(),

  email: z.string().email(),

  first_name: z.string().optional(),

  last_name: z.string().optional(),

  ownership_type: ownershipType.nullish(),

  parent_id: z.string().optional(),

  parent_type: applicationType,

  phone_number: z.string().optional(),

  plaid_public_token: z.string().nullish().optional(),

  plaid_manual_verification: z.boolean().optional(),

  ssn: DBDEncryptedField.or(z.string()).optional(),

  ssn_encrypted: z.string().optional(),
});

export type PayeeIndividual = z.infer<typeof PayeeIndividual>;

export const PayeeCompany = z.object({
  application_id: z.string(),

  bank_identity_manual_account: z
    .object({
      class_type: z.string(),
      id: z.string(),
      mask: z.string(),
      name: z.string(),
      subtype: z.string(),
      type: z.string(),
      verification_status: z.string(),
    })
    .optional(),

  business_address: LocationReference.optional(),

  email: z.string().email(),

  ein: DBDEncryptedField.or(z.string()).optional(),

  encrypted_tax_id: z.string().optional(),

  legal_name: z.string().min(2).max(100),

  name: z.string().min(2).max(100),

  owners: ownersSchema,

  ownership_type: ownershipType.nullish(),

  parent_id: z.string().optional(),

  parent_type: applicationType,

  plaid_public_token: z.string().nullish().optional(),

  plaid_manual_verification: z.boolean().optional(),
});
export type PayeeCompany = z.infer<typeof PayeeCompany>;

export const PayeeApplicationSubmittedEvent = PayeeCompany.or(PayeeIndividual);
export type PayeeApplicationSubmittedEvent = z.infer<typeof PayeeApplicationSubmittedEvent>;

export const PayeeOnboardingInput = z.object({
  application_id: z.string(),

  payee_id: z.string(),
  payee_level: applicationType,

  tenant_id: z.string(),
  tenant_name: z.string(),

  partner_id: z.string(),
  partner_name: z.string(),

  business_name: z.string().optional(),
  business_id: z.string().optional(),

  submitted_at: z.string(),

  company: PayeeCompany.optional(),
  individual: PayeeIndividual.optional(),

  comments: z.string().optional(),
  ip_address: z.string().optional().default(''),
});

export type PayeeOnboardingInput = z.infer<typeof PayeeOnboardingInput>;
