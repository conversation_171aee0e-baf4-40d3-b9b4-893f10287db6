import { AuthUser } from '@dbd/core-types/domain/auth.domain';
import { Kysely } from 'kysely';

import { DB } from '../modules/database/gen/db-types.js';
import { EventRepository } from '../modules/entities/event/event.repository.js';
import { Outboxer } from '../modules/providers/outboxer/outboxer.entity.js';
import { TaktileClient } from '../modules/providers/taktile/taktile.client.js';
import { TaktileService } from '../modules/providers/taktile/taktile.service.js';

declare module 'fastify' {
  interface FastifyInstance {
    decisionStore: Kysely<DB>;
    taktileClient: TaktileClient;
    taktileService: TaktileService;
    eventRepo: EventRepository;
    outboxer: Outboxer;
  }

  interface FastifyRequest {
    user: AuthUser;
  }
}

export {};
