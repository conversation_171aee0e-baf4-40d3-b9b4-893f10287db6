import { errorHandlerPlugin } from '@dbd/errors/error-handler-plugin';
import { FlagsmithServerClient } from '@dbd/flagsmith';
import { initServer } from '@ts-rest/fastify';
import fastify, { FastifyInstance } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { afterEach, beforeAll, describe, it, vi } from 'vitest';

import { NoopEventRepository } from '../../../entities/event/event.entity.js';
import { PayeeCase } from '../../../entities/payee-case/payee-case.entity.js';
import { PayeeDecision } from '../../../entities/payee-decision/payee-decision.entity.js';
import { NoopOutboxer } from '../../../providers/outboxer/outboxer.entity.js';
import { initializeContractInstance } from '../../../test/fastify.js';
import { mockImpl, serviceContract } from '../../contract.js';
import { PayeeDisposition } from './payee-disposition.route.js';

const { mockDBDClient } = vi.hoisted(() => ({
  mockDBDClient: {
    contractors: {
      approve: vi.fn().mockResolvedValue({ id: 'cntrc_123', status: 'approved' }),
      reject: vi.fn().mockResolvedValue({ id: 'cntrc_123', status: 'rejected' }),
    },
  },
}));

vi.mock('@dbd/service-client-library', () => ({
  DBDClient: vi.fn(() => mockDBDClient),
}));

vi.mock('../../../entities/provider-message/provider-message.repository.js');

const { useSaveCase } = vi.hoisted(() => ({
  useSaveCase: vi.fn().mockReturnValue(Promise.resolve({ uid: 'pyc_7b3bb23' })),
}));

const { useSaveDecision } = vi.hoisted(() => ({
  useSaveDecision: vi.fn().mockReturnValue(Promise.resolve({ uid: 'pyd_6ec6081b' })),
}));

describe('Decisioning Service | Payee Disposition', () => {
  let instance: FastifyInstance;
  let evtrepo: NoopEventRepository;
  const srv = initServer();

  afterEach(() => {
    vi.resetAllMocks();
    mockDBDClient.contractors.approve.mockClear();
    mockDBDClient.contractors.reject.mockClear();
  });

  beforeAll(async () => {
    instance = fastify({ logger: { level: 'silent' } });
    await instance.register(errorHandlerPlugin);
    instance.register(fastifyPlugin(initializeContractInstance));

    evtrepo = new NoopEventRepository();
    const outboxer = new NoopOutboxer(evtrepo);
    instance.decorate<NoopOutboxer>('outboxer', outboxer);

    instance.decorate(
      'flagsmithClient',
      new FlagsmithServerClient({
        environmentKey: 'tst.flgsmithkey',
        enableAnalytics: false,
      }),
    );

    const router = srv.router(serviceContract, {
      ...mockImpl(srv, serviceContract),
      postPayeeDisposition: PayeeDisposition(srv),
    });

    srv.registerRouter(serviceContract, router, instance);
  });

  describe('ManualCaseCreationReview', () => {
    it('records a payee case successfully', async ({ expect }) => {
      PayeeCase.save = useSaveCase;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.case.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
    });
  });

  describe('DecisionOutcome', () => {
    it('records a payee application decision successfully', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
    });

    it('calls contractor approve when decision is approved', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return an approved decision
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'approved',
            decision_comments: 'auto-approved by system',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.approve).toHaveBeenCalledWith('ctrcapp_test123', 'auto-approved by system');
    });

    it('does not call contractor approve when decision is not approved', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return a rejected decision
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'rejected',
            decision_comments: 'insufficient documentation',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.approve).not.toHaveBeenCalled();
    });

    it('calls contractor approve with undefined comments when no comments provided', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return an approved decision without comments
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'approved',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.approve).toHaveBeenCalledWith('ctrcapp_test123', undefined);
    });

    it('calls contractor reject when decision is declined', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return a declined decision
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'declined_kyb',
            decision_comments: 'KYB verification failed',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.reject).toHaveBeenCalledWith('ctrcapp_test123', 'KYB verification failed');
      expect(mockDBDClient.contractors.approve).not.toHaveBeenCalled();
    });

    it('calls contractor reject for different decline reasons', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const declineReasons = ['declined_kyc', 'declined_credit', 'declined_web', 'declined_expired', 'declined_other'];

      for (const reason of declineReasons) {
        const payload = JSON.parse(
          await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
        );

        payload.data.object.event.flow = 'payee-onboarding';

        // Mock the taktile service to return a declined decision
        instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
          error: null,
          result: {
            metadata: {
              entity_id: 'ctrcapp_test123',
            },
            data: {
              app_decision: reason,
              decision_comments: `Application declined due to ${reason}`,
            },
          },
        });

        const results = await instance.inject({
          body: payload,
          headers: {
            'content-type': 'application/json',
          },
          method: 'POST',
          url: '/disposition/payee_boarding',
        });

        expect(results.statusCode).toBe(200);
        expect(mockDBDClient.contractors.reject).toHaveBeenCalledWith(
          'ctrcapp_test123',
          `Application declined due to ${reason}`,
        );
        expect(mockDBDClient.contractors.approve).not.toHaveBeenCalled();

        // Clear mocks for next iteration
        mockDBDClient.contractors.reject.mockClear();
        mockDBDClient.contractors.approve.mockClear();
      }
    });

    it('calls contractor reject with undefined comments when no comments provided for declined decision', async ({
      expect,
    }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return a declined decision without comments
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'declined_other',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.reject).toHaveBeenCalledWith('ctrcapp_test123', undefined);
      expect(mockDBDClient.contractors.approve).not.toHaveBeenCalled();
    });

    it('does not call contractor approve or reject for non-approved/declined decisions', async ({ expect }) => {
      PayeeDecision.save = useSaveDecision;

      const payload = JSON.parse(
        await readFile(join(__dirname, '../../../test/events/taktile-message.decision-outcome.json'), 'utf8'),
      );

      payload.data.object.event.flow = 'payee-onboarding';

      // Mock the taktile service to return a pending decision
      instance.taktileService.fetchDecision = vi.fn().mockResolvedValue({
        error: null,
        result: {
          metadata: {
            entity_id: 'ctrcapp_test123',
          },
          data: {
            app_decision: 'pending',
            decision_comments: 'Under review',
          },
        },
      });

      const results = await instance.inject({
        body: payload,
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        url: '/disposition/payee_boarding',
      });

      expect(results.statusCode).toBe(200);
      expect(mockDBDClient.contractors.reject).not.toHaveBeenCalled();
      expect(mockDBDClient.contractors.approve).not.toHaveBeenCalled();
    });
  });
});
