import { ProblemJ<PERSON><PERSON> } from '@dbd/errors';
import { DBDClient } from '@dbd/service-client-library';
import { initServer } from '@ts-rest/fastify';
import { FastifyBaseLogger } from 'fastify';

import { EventMetadata } from '../../../../schemas/taktile-webhooks.js';
import { asExternalError } from '../../../entities/external-error.mapper.js';
import { PayeeCase } from '../../../entities/payee-case/payee-case.entity.js';
import { PayeeCaseRepository } from '../../../entities/payee-case/payee-case.repository.js';
import { PayeeDecision } from '../../../entities/payee-decision/payee-decision.entity.js';
import { PayeeDecisionRepository } from '../../../entities/payee-decision/payee-decision.repository.js';
import { ProviderMessage } from '../../../entities/provider-message/provider-message.entity.js';
import { ProviderMessageRepository } from '../../../entities/provider-message/provider-message.repository.js';
import { APIError, ErrorName, handleAPIError } from '../../../providers/provider-errors.js';
import { TaktileService } from '../../../providers/taktile/taktile.service.js';
import { serviceContract } from '../../contract.js';
import { TaktileAPIErrorMapper } from '../router.js';

export const PayeeDisposition = (server: ReturnType<typeof initServer>) => {
  return server.route(serviceContract.postPayeeDisposition, async ({ request, reply, body }) => {
    if (!request.server.taktileService) {
      request.log.error('taktile services not available');
      reply.header(...ProblemJSON);
      return handleAPIError(
        new APIError('Internal server error', {
          cause: {
            detail: 'an unexpected error occurred',
            instance: request.url,
            type: ErrorName.Enum.INTERNAL_SERVER_ERROR,
            title: 'Internal Server Error',
            status: 500,
          },
        }),
      );
    }

    const conn = request.server.decisionStore;

    const caseRepo = new PayeeCaseRepository(conn, request.server.outboxer, request.log);
    const decisionRepo = new PayeeDecisionRepository(conn, request.server.outboxer, request.log);
    const providerMessageRepo = new ProviderMessageRepository(conn, request.server.outboxer, request.log);

    const {
      data: { object },
    } = body;

    if (!object.event.entity_id) {
      reply.header(...ProblemJSON);
      return handleAPIError(
        new APIError(`application ID not found`, {
          cause: {
            instance: request.url,
            type: ErrorName.Enum.BAD_REQUEST,
            title: 'Bad Request',
            status: 400,
          },
        }),
      );
    }

    let messageStatus;
    // Only DecisionOutcome have this field
    // set per the OpenAPI docs
    if ('status' in object.event) {
      messageStatus = object.event.status;
    }

    let orchestratorId = object.event.decision_id;
    if (object.type === 'ManualReviewCaseCreation') {
      orchestratorId = object.event.case_id;
    }

    const providerRes = await ProviderMessage.patch(
      providerMessageRepo,
      request.server.taktileService,
      request.user,
      object.event.flow,
      {
        applicationId: object.event.entity_id ?? object.event.application_id,
        messageType: object.type,
        orchestratorId,
        status: messageStatus,
        value: object.event,
      },
      request.server.outboxer,
    ).catch((reason: unknown) => {
      request.log.error(reason);
    });

    if (providerRes) {
      request.log.error(providerRes.error);
    }

    if (!canProcess(object.event)) {
      return {
        status: 200,
        body: {},
      };
    }

    if (object.type === 'DecisionOutcome') {
      return await processDecisionOutcome(
        request.user,
        decisionRepo,
        request.log,
        request.server.taktileService,
        request.url,
        object.event.decision_id,
        object.event.flow,
      );
    } else if (object.type === 'ManualReviewCaseCreation') {
      return await processCaseCreation(
        request.user,
        caseRepo,
        request.log,
        request.server.taktileService,
        request.url,
        object.event.entity_id,
        object.event.case_id,
        object.event.flow,
      );
    } else {
      // ts-rest does not infer a number overlaps with
      // the HTTP status codes nor does the lib provide
      // viable public types to use for 'satisfies'
      return { status: 200 as any, body: {} };
    }
  });
};

async function processDecisionOutcome(
  authCtx: Record<string, unknown>,
  decisionRepo: PayeeDecisionRepository,
  logger: FastifyBaseLogger,
  client: TaktileService,
  requestURL: string,
  decisionID: string,
  flowSlug: string,
) {
  const { error, result: decisionData } = await client.fetchDecision(decisionID, flowSlug);
  if (error) {
    logger.error(error);
    const err = TaktileAPIErrorMapper(error.name, requestURL);
    return handleAPIError(err);
  }

  logger.info({ outcome: decisionData });

  const applicationId = decisionData.metadata.entity_id ?? decisionData.metadata.application_id;
  if (!applicationId) {
    return handleAPIError(
      new APIError(`decisioned application with no ID and status = ${decisionData.data.app_decision} `, {
        cause: {
          detail: `Decisioned application with no ID and status = ${decisionData.data.app_decision} `,
          instance: requestURL,
          type: ErrorName.Enum.INTERNAL_SERVER_ERROR,
          title: 'Internal Server Error',
          status: 500,
        },
      }),
    );
  }

  const res = await PayeeDecision.save(authCtx, decisionRepo, applicationId, decisionID, decisionData);
  if (decisionData.data.app_decision === 'approved') {
    logger.info({ approved: decisionData });
    const dbdClient = new DBDClient();
    dbdClient.contractors.approve(applicationId, decisionData.data.decision_comments);
  }
  if (res && Object.hasOwn(res, 'error')) {
    const writeError = res.error;
    logger.error(writeError);
    //@ts-expect-error the object is checked for its presence and has an identity
    const { status: errStatus, type: errType, title } = asExternalError(writeError.name);
    const err = new APIError('something went wrong', {
      cause: {
        detail: writeError?.message,
        instance: requestURL,
        status: errStatus,
        title,
        type: errType,
      },
    });

    return handleAPIError(err);
  }

  return {
    body: {},
    status: 200,
  };
}

async function processCaseCreation(
  authCtx: Record<string, unknown>,
  caseRepo: PayeeCaseRepository,
  logger: FastifyBaseLogger,
  client: TaktileService,
  requestURL: string,
  applicationId: string,
  caseID: string,
  flowSlug: string,
) {
  const { error, result: caseData } = await client.fetchCase(caseID, flowSlug);
  if (error) {
    logger.error(error);
    const err = TaktileAPIErrorMapper(error.name, requestURL);
    return handleAPIError(err);
  }

  logger.info({ processOnboardingCaseCreation: caseData });

  const parentId = caseData.business_id ?? caseData.partner_id;

  const res = await PayeeCase.save(authCtx, caseRepo, parentId, applicationId, caseID, caseData);
  if (res && Object.hasOwn(res, 'error')) {
    const writeError = res.error;
    logger.error(writeError);
    //@ts-expect-error the object is checked for its presence and has an identity
    const { status: errStatus, type: errType, title } = asExternalError(writeError.name);
    return handleAPIError(
      new APIError('something went wrong', {
        cause: {
          detail: writeError?.message,
          instance: requestURL,
          type: errType,
          title: title,
          status: errStatus,
        },
      }),
    );
  }

  return {
    body: {},
    // FIXME: this appears to be the only way to ensure the compiler
    // agrees that a 200 is a valid HTTP status code. This suggests
    // the typings are wrong, will fix.
    status: 200 as any,
  };
}

function canProcess(evt: EventMetadata) {
  // Do not process DecisionOutcomes that have errors
  if (evt.status && evt.status === 'error') {
    return false;
  }
  return true;
}
