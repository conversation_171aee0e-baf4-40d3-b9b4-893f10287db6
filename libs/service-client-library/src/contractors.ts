import type {
  ContractorApplicationLinkDTO,
  ContractorGetQuerystringDTO,
  ContractorResponseDTO,
  ContractorSummaryListDTO,
  CreateCompanyContractorDTO,
  CreateContractorRequestDTO,
  CreateIndividualContractorDTO,
} from '@dbd/core-types/dto/contractors.dto';
import type { ContractorParentType } from '@dbd/core-types/enums/contractor.enums';
import type { AxiosInstance } from 'axios';

import handleApiError from './handle-api-error.js';
import { GET_ALL_LIST_ITEMS_PAGE_SIZE } from './utils/index.js';

export interface IContractors {
  list(query: ContractorGetQuerystringDTO): Promise<ContractorSummaryListDTO>;
  activate(id: string): Promise<ContractorResponseDTO>;
  deactivate(id: string): Promise<ContractorResponseDTO>;
  retrieve(id: string): Promise<ContractorResponseDTO>;
  generateLink(parentId: string, parentType: ContractorParentType): Promise<ContractorApplicationLinkDTO>;
  /** Get contractor application link status */
  applicationLinkStatus(id: string): Promise<ContractorApplicationLinkDTO>;
  updateBankAccount(id: string, bankAccountId: string): Promise<ContractorResponseDTO>;
  updateApplicationBankAccount(applicationId: string, bankAccountId: string): Promise<object>;
  create(contractor: CreateContractorRequestDTO): Promise<ContractorResponseDTO>;
  approve(applicationId: string, comments?: string): Promise<ContractorResponseDTO>;
  reject(applicationId: string, comments?: string): Promise<ContractorResponseDTO>;
}

export class DBDContractorsService implements IContractors {
  private instance: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl: string, axiosInstance: AxiosInstance) {
    this.instance = axiosInstance;
    this.baseUrl = `${baseUrl}/contractors`;
  }

  public async list(options: ContractorGetQuerystringDTO): Promise<ContractorSummaryListDTO> {
    const params = {
      page: 0,
      size: GET_ALL_LIST_ITEMS_PAGE_SIZE,
      sort_by: 'NAME',
      sort_direction: 'ASC',
      ...options,
    };

    try {
      const response = await this.instance.get<ContractorSummaryListDTO>(this.baseUrl, { params });
      return response.data;
    } catch (err) {
      handleApiError(err);
    }
  }

  public async activate(id: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/${id}/activate`;
    const response = await this.instance.patch<ContractorResponseDTO>(url);
    return response.data;
  }

  public async approve(applicationId: string, comments?: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/application/${applicationId}/approve`;
    const response = await this.instance.patch<ContractorResponseDTO>(url, { comments });
    return response.data;
  }

  public async reject(applicationId: string, comments?: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/application/${applicationId}/reject`;
    const response = await this.instance.patch<ContractorResponseDTO>(url, { comments });
    return response.data;
  }

  public async deactivate(id: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/${id}/deactivate`;
    const response = await this.instance.patch<ContractorResponseDTO>(url);
    return response.data;
  }

  public async updateBankAccount(id: string, bankAccountId: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/${id}`;
    const body = {
      bank_account_id: bankAccountId,
    };
    const response = await this.instance.patch<ContractorResponseDTO>(url, body);
    return response.data;
  }

  public async updateApplicationBankAccount(applicationId: string, bankAccountId: string): Promise<object> {
    const url = `${this.baseUrl}/application/${applicationId}/bank-account/${bankAccountId}`;
    const response = await this.instance.put<object>(url);
    return response.data;
  }

  public async retrieve(id: string): Promise<ContractorResponseDTO> {
    const url = `${this.baseUrl}/${id}`;
    try {
      const response = await this.instance.get<ContractorResponseDTO>(url);
      return response.data;
    } catch (err) {
      handleApiError(err);
    }
  }

  public async generateLink(parentId: string, parentType: ContractorParentType): Promise<ContractorApplicationLinkDTO> {
    const url = `${this.baseUrl}/link`;
    const response = await this.instance.post<ContractorApplicationLinkDTO>(url, {
      parent_id: parentId,
      parent_type: parentType,
    });
    return response.data;
  }

  public async applicationLinkStatus(id: string): Promise<ContractorApplicationLinkDTO> {
    const url = `${this.baseUrl}/application/${id}`;
    try {
      const response = await this.instance.get<ContractorApplicationLinkDTO>(url);
      return response.data;
    } catch (err) {
      handleApiError(err);
    }
  }

  public async create(contractor: CreateContractorRequestDTO): Promise<ContractorResponseDTO> {
    const isCompanyContractor = !(
      contractor as {
        individual: CreateIndividualContractorDTO;
      }
    ).individual;

    const url = `${this.baseUrl}/${isCompanyContractor ? 'company' : 'individual'}`;
    const { individual, company } = contractor as {
      individual?: CreateIndividualContractorDTO;
      company?: CreateCompanyContractorDTO;
    };

    try {
      const body = isCompanyContractor ? company : individual;
      const response = await this.instance.post<ContractorResponseDTO>(url, body);
      return response.data;
    } catch (err) {
      handleApiError(err);
    }
  }
}
