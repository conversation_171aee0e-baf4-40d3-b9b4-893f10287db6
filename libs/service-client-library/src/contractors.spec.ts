import { ContractorParentType } from '@dbd/core-types/enums/contractor.enums';
import { AxiosInstance } from 'axios';
import { mockDeep, mockReset } from 'jest-mock-extended';

import { DBDContractorsService } from './contractors.js';
import { MOCK_COMPANY_CONTRACTOR, MOCK_INDIVIDUAL_CONTRACTOR } from './mock/contractor.js';

const mockedAxiosServerAuthInstance = mockDeep<AxiosInstance>();
jest.mock('./axios-config', () => {
  return {
    getAxiosInstance: () => mockedAxiosServerAuthInstance,
  };
});

describe('DBDClient Contractors', () => {
  const service = new DBDContractorsService('test', mockedAxiosServerAuthInstance);

  beforeEach(() => {
    mockReset(mockedAxiosServerAuthInstance);
  });

  it('should activate a contractor', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce((path) => {
      const data = path.includes('deactivate') ? MOCK_INDIVIDUAL_CONTRACTOR : MOCK_COMPANY_CONTRACTOR;
      return Promise.resolve({
        data,
      });
    });
    const results = await service.activate('cntrc_1234');
    expect(results).toStrictEqual(MOCK_COMPANY_CONTRACTOR);
  });

  it('should approve a contractor application', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce(() => {
      return Promise.resolve({
        data: MOCK_COMPANY_CONTRACTOR,
      });
    });
    const results = await service.approve('ctrcapp_1234', 'Approved by system');
    expect(results).toStrictEqual(MOCK_COMPANY_CONTRACTOR);
    expect(mockedAxiosServerAuthInstance.patch).toHaveBeenCalledWith(
      'test/contractors/application/ctrcapp_1234/approve',
      { comments: 'Approved by system' },
    );
  });

  it('should reject a contractor application', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce(() => {
      return Promise.resolve({
        data: MOCK_INDIVIDUAL_CONTRACTOR,
      });
    });
    const results = await service.reject('ctrcapp_1234', 'Rejected due to insufficient documentation');
    expect(results).toStrictEqual(MOCK_INDIVIDUAL_CONTRACTOR);
    expect(mockedAxiosServerAuthInstance.patch).toHaveBeenCalledWith(
      'test/contractors/application/ctrcapp_1234/reject',
      { comments: 'Rejected due to insufficient documentation' },
    );
  });

  it('should approve a contractor application without comments', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce(() => {
      return Promise.resolve({
        data: MOCK_COMPANY_CONTRACTOR,
      });
    });
    const results = await service.approve('ctrcapp_1234');
    expect(results).toStrictEqual(MOCK_COMPANY_CONTRACTOR);
    expect(mockedAxiosServerAuthInstance.patch).toHaveBeenCalledWith(
      'test/contractors/application/ctrcapp_1234/approve',
      { comments: undefined },
    );
  });

  it('should reject a contractor application without comments', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce(() => {
      return Promise.resolve({
        data: MOCK_INDIVIDUAL_CONTRACTOR,
      });
    });
    const results = await service.reject('ctrcapp_1234');
    expect(results).toStrictEqual(MOCK_INDIVIDUAL_CONTRACTOR);
    expect(mockedAxiosServerAuthInstance.patch).toHaveBeenCalledWith(
      'test/contractors/application/ctrcapp_1234/reject',
      { comments: undefined },
    );
  });
  it('should deactivate a contractor', async () => {
    mockedAxiosServerAuthInstance.patch.mockImplementationOnce((path) => {
      const data = path.includes('deactivate') ? MOCK_INDIVIDUAL_CONTRACTOR : MOCK_COMPANY_CONTRACTOR;
      return Promise.resolve({
        data,
      });
    });
    const results = await service.deactivate('cntrc_1234');
    expect(results).toStrictEqual(MOCK_INDIVIDUAL_CONTRACTOR);
  });

  it('should get a list of contractors', async () => {
    mockedAxiosServerAuthInstance.get.mockResolvedValueOnce({
      data: {
        data: [MOCK_COMPANY_CONTRACTOR, MOCK_INDIVIDUAL_CONTRACTOR],
        meta: { limit: 10, has_more: false },
      },
    });

    const results = await service.list({
      parent_id: 'bus_123',
      parent_type: ContractorParentType.BUSINESS,
    });
    expect(results.data).toHaveLength(2);
  });

  it('should update the contractor bank account', async () => {
    const bankAccountId = 'bnk_1234';
    const contractorId = 'cntrc_1234';
    mockedAxiosServerAuthInstance.patch.mockImplementation((url, data) => {
      const correctPath = url.includes(contractorId);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const correctData = (data as any).bank_account_id === bankAccountId;
      const returnData = correctPath && correctData ? MOCK_COMPANY_CONTRACTOR : MOCK_INDIVIDUAL_CONTRACTOR;
      return Promise.resolve({
        data: returnData,
      });
    });

    const results = await service.updateBankAccount(contractorId, bankAccountId);
    expect(results).toStrictEqual(MOCK_COMPANY_CONTRACTOR);
  });
});
