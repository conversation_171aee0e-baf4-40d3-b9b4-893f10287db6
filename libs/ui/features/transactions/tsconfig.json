{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../tailwind-components"}, {"path": "../data-table"}, {"path": "../../../zod-types"}, {"path": "../../../next-sessions"}, {"path": "../../../zod-types-common"}, {"path": "../../../reporting/common/server"}, {"path": "../../../reporting/transactions"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}