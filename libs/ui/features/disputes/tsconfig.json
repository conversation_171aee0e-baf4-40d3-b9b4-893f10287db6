{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../core-types"}, {"path": "../forms"}, {"path": "../../../tailwind-components"}, {"path": "../../../zod-types"}, {"path": "../payments"}, {"path": "../data-table"}, {"path": "../../../zod-types-common"}, {"path": "../../../reporting/disputes"}, {"path": "../../../reporting/common/server"}, {"path": "../../../next-sessions"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}