{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../reporting/merchant-applications"}, {"path": "../forms"}, {"path": "../../../core-types"}, {"path": "../../../reporting/common/server"}, {"path": "../../../zod-types-common"}, {"path": "../../../zod-types"}, {"path": "../../../reporting/partners"}, {"path": "../../../next-sessions"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}