{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../tailwind-components"}, {"path": "../../../core-types"}, {"path": "../../../zod-types"}, {"path": "../data-table"}, {"path": "../../../zod-types-common"}, {"path": "../../../reporting/payouts"}, {"path": "../../../reporting/payout-events"}, {"path": "../../../reporting/common/server"}, {"path": "../../../next-sessions"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}