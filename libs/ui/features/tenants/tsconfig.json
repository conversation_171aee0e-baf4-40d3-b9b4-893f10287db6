{"compilerOptions": {"baseUrl": ".", "jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "files": [], "include": [], "references": [{"path": "../../../utils"}, {"path": "../../../core-types"}, {"path": "../../../zod-types-common"}, {"path": "../../../service-client-library"}, {"path": "../common"}, {"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../../tsconfig.base.json"}